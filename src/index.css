@import './App.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Brand colors - from big360Color.brand */
    --brand: 259 63% 63%;  /* #7C47E6 - brand.500 */
    --brand-secondary: 259 100% 76%;  /* #CFBEFF - brand.100 */
    --brand-foreground: 259 100% 98%;  /* #F4F0FF - brand.00 */

    /* Background colors - from big360Color.neutral */
    --background: 0 0% 99%;  /* #FDFDFD - neutral.00 */
    --background-foreground: 220 7% 12%;  /* #1E1F21 - neutral.900 */
    --foreground: 220 7% 12%;  /* #1E1F21 - neutral.900 */

    /* Card colors - from big360Color.neutral */
    --card: 0 0% 99%;  /* #FDFDFD - neutral.00 */
    --card-foreground: 220 7% 12%;  /* #1E1F21 - neutral.900 */

    /* Popover colors - from big360Color.neutral */
    --popover: 0 0% 99%;  /* #FDFDFD - neutral.00 */
    --popover-foreground: 220 7% 12%;  /* #1E1F21 - neutral.900 */

    /* Primary colors - from big360Color.brand */
    --primary-crm: 259 63% 63%;  /* #7C47E6 - brand.500 */
    --primary-foreground-crm: 259 100% 98%;  /* #F4F0FF - brand.00 */

    /* Hover colors - from big360Color.neutral */
    --hover-filter: 210 4% 91%;  /* #E6E7E9 - neutral.100 */
    --hover-filter-foreground: 215 6% 33%;  /* #4E5255 - neutral.700 */

    /* Text colors - from big360Color.neutral */
    --text-primary-crm: 220 7% 12%;  /* #1E1F21 - neutral.900 */
    --text-foreground: 215 6% 33%;  /* #4E5255 - neutral.700 */

    /* Secondary colors - from big360Color.neutral */
    --secondary: 210 4% 86%;  /* #D9DBDE - neutral.200 */
    --secondary-foreground-crm: 215 6% 45%;  /* #6F7377 - neutral.600 */

    /* Tertiary colors - from big360Color.neutral */
    --tertiary: 210 4% 91%;  /* #E6E7E9 - neutral.100 */
    --tertiary-foreground: 215 6% 33%;  /* #4E5255 - neutral.700 */

    /* Muted colors - from big360Color.neutral */
    --muted: 210 4% 91%;  /* #E6E7E9 - neutral.100 */
    --muted-foreground: 215 6% 45%;  /* #6F7377 - neutral.600 */

    /* Accent colors - from big360Color.brand */
    --accent: 259 100% 76%;  /* #CFBEFF - brand.100 */
    --accent-foreground: 259 63% 50%;  /* #45257F - brand.800 */

    /* Destructive colors - from big360Color.danger */
    --destructive: 0 97% 61%;  /* #F84242 - danger.500 */
    --destructive-foreground: 0 100% 98%;  /* #FFF4F4 - danger.00 */

    /* Border and input colors - from big360Color.neutral */
    --border: 210 4% 86%;  /* #D9DBDE - neutral.200 */
    --input: 210 4% 86%;  /* #D9DBDE - neutral.200 */

    /* Avatar color - from big360Color.brand */
    --avatar: 259 100% 87%;  /* #A37EFF - brand.300 */

    /* Ring color - from big360Color.brand */
    --ring: 259 100% 68%;  /* #8F5CFF - brand.400 */

    /* Create colors - from big360Color.success */
    --create: 158 60% 36%;  /* #2BB684 - success.500 */
    --create-foreground: 158 100% 98%;  /* #F3FDF7 - success.00 */

    /* Error colors - from big360Color.danger */
    --error: 0 97% 61%;  /* #F84242 - danger.500 */
    --error-text: 0 75% 45%;  /* #BF2D2D - danger.700 */

    /* Success colors - from big360Color.success */
    --success: 158 60% 36%;  /* #2BB684 - success.500 */
    --success-text: 158 75% 45%;  /* #197A59 - success.700 */
    --success-hover: 158 63% 30%;  /* #22996F - success.600 */

    /* Delete colors - from big360Color.danger */
    --delete: 0 97% 61%;  /* #F84242 - danger.500 */
    --delete-foreground: 0 100% 98%;  /* #FFF4F4 - danger.00 */

    /* Warning colors - from big360Color.warning */
    --warning: 48 100% 76%;  /* #F5B200 - warning.500 */
    --warning-bg: 48 100% 92%;  /* #FFF5D6 - warning.50 */
    --warning-text: 39 100% 36%;  /* #9E7200 - warning.800 */

    /* Info colors - from big360Color.info */
    --info: 207 67% 62%;  /* #4F9EF0 - info.500 */
    --info-bg: 207 100% 94%;  /* #E4F0FE - info.50 */
    --info-text: 207 69% 34%;  /* #2A5490 - info.800 */

    /* Standard colors - from big360Color.neutral */
    --standard: 210 4% 78%;  /* #C5C8CB - neutral.300 */
    --standard-text: 215 6% 45%;  /* #6F7377 - neutral.600 */

    /* Filter text - from big360Color.neutral */
    --filter-text: 215 6% 45%;  /* #6F7377 - neutral.600 */

    /* Cell hover - from big360Color.neutral */
    --cell-hover: 0 0% 96%;  /* #F5F5F5 - neutral.50 */

    /* Chart colors - using semantic colors from big360Color */
    --chart-1: 259 63% 63%;  /* brand.500 */
    --chart-2: 158 60% 36%;  /* success.500 */
    --chart-3: 48 100% 76%;  /* warning.500 */
    --chart-4: 207 67% 62%;  /* info.500 */
    --chart-5: 0 97% 61%;   /* danger.500 */

    /* Other variables */
    --radius: 0.5rem;
    --shadow-light: rgba(9, 10, 13, 0.02);
    --shadow-medium: rgba(9, 10, 13, 0.1);
    --shadow-filter: 0px 0px 32px 0px var(--shadow-light), 0px 4px 20px -8px var(--shadow-medium);
    --disabled: 210 4% 78%;  /* #C5C8CB - neutral.300 */
    --overlay: rgba(30, 31, 33, 0.28);  /* alphaBlack.48 equivalent */
    --box-icon-overlay: rgba(253, 253, 253, 0.40);  /* alphaWhite.64 equivalent */
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer components {
  .border-border {
    border-color: hsl(var(--border));
  }

  .bg-background {
    background-color: hsl(var(--background));
  }

  .text-foreground {
    color: hsl(var(--foreground));
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Style scrollbar */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  /* background: red; */
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 5px;
}

*:hover::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.scroll-bottom::-webkit-scrollbar-thumb {
  background: #888 !important;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type='number'] {
  -moz-appearance: textfield;
}

body {
  pointer-events: all !important;
}

:where([data-sonner-toaster]) {
  font-family: inherit !important;
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
